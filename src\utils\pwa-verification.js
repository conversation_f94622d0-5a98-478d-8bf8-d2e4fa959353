/**
 * PWA Mobile Verification Script
 * Helps debug and verify PWA mobile patterns are working correctly
 */

export const verifyPWAIntegration = () => {
  const results = {
    isMobile: window.innerWidth <= 768,
    windowWidth: window.innerWidth,
    touchSupported: 'ontouchstart' in window,
    vibrationSupported: 'vibrate' in navigator,
    pwaInteractionsLoaded: !!window.pwaInteractions,
    pwaElementsPresent: {
      bottomNav: !!document.querySelector('.pwa-bottom-nav'),
      fab: !!document.querySelector('.pwa-fab'),
      cards: document.querySelectorAll('.pwa-mobile-card').length,
      appShell: !!document.querySelector('.pwa-app-shell')
    },
    cssLoaded: {
      pwaStyles: !!document.querySelector('style[data-vite-dev-id*="mobile-pwa-2025"]') || 
                  !!Array.from(document.styleSheets).some(sheet => 
                    sheet.href && sheet.href.includes('mobile-pwa-2025')),
      bottomNavStyles: !!document.querySelector('style[data-vite-dev-id*="PWABottomNavigation"]') ||
                       !!Array.from(document.styleSheets).some(sheet => 
                         sheet.href && sheet.href.includes('PWABottomNavigation'))
    }
  };

  console.group('🔍 PWA Mobile Integration Verification');
  console.log('📱 Device Detection:', {
    isMobile: results.isMobile,
    windowWidth: results.windowWidth,
    touchSupported: results.touchSupported,
    vibrationSupported: results.vibrationSupported
  });
  
  console.log('⚙️ PWA Components:', {
    interactionsLoaded: results.pwaInteractionsLoaded,
    elementsPresent: results.pwaElementsPresent
  });
  
  console.log('🎨 CSS Loading:', results.cssLoaded);
  
  // Recommendations
  const recommendations = [];
  
  if (!results.isMobile) {
    recommendations.push('📏 Resize browser window to ≤768px to see PWA patterns');
  }
  
  if (results.isMobile && !results.pwaElementsPresent.bottomNav) {
    recommendations.push('🔄 Hard refresh (Ctrl+F5) to load PWA components');
  }
  
  if (!results.pwaInteractionsLoaded) {
    recommendations.push('⚠️ PWA interactions not loaded - check console for errors');
  }
  
  if (recommendations.length > 0) {
    console.log('💡 Recommendations:', recommendations);
  } else if (results.isMobile) {
    console.log('✅ PWA integration appears to be working correctly!');
  }
  
  console.groupEnd();
  
  return results;
};

// Auto-run verification in development
if (process.env.NODE_ENV === 'development') {
  setTimeout(() => {
    verifyPWAIntegration();
  }, 2000); // Wait 2 seconds for components to load
}

// Make available globally for manual testing
window.verifyPWA = verifyPWAIntegration;
